from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.conf import settings
from .models import License, SKU, Order, Setting


@admin.register(Setting)
class SettingAdmin(admin.ModelAdmin):
    list_display = ["key", "value_preview", "description", "updated_at"]
    list_filter = ["created_at", "updated_at"]
    search_fields = ["key", "description"]
    readonly_fields = ["created_at", "updated_at"]

    def value_preview(self, obj):
        """值预览"""
        if len(obj.value) > 50:
            return obj.value[:50] + "..."
        return obj.value

    value_preview.short_description = "配置值"


@admin.register(SKU)
class SKUAdmin(admin.ModelAdmin):
    list_display = [
        "sku_id",
        "name",
        "app",
        "renewal_days",
        "price",
        "is_active",
        "updated_at",
    ]
    list_filter = ["app", "is_active", "created_at"]
    search_fields = ["sku_id", "name", "app"]
    readonly_fields = ["created_at", "updated_at"]
    list_editable = ["is_active"]

    fieldsets = (
        ("基本信息", {"fields": ("sku_id", "name", "app")}),
        ("商品配置", {"fields": ("renewal_days", "price", "is_active")}),
        (
            "时间信息",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(License)
class LicenseAdmin(admin.ModelAdmin):
    list_display = [
        "email",
        "app",
        "plan",
        "expires_at",
        "is_expired_display",
        "is_active",
        "updated_at",
    ]
    list_filter = ["app", "plan", "is_active", "created_at"]
    search_fields = ["email", "app"]
    readonly_fields = ["created_at", "updated_at", "is_expired_display"]
    list_editable = ["is_active"]
    date_hierarchy = "expires_at"

    fieldsets = (
        ("许可证信息", {"fields": ("app", "email", "plan")}),
        ("时间配置", {"fields": ("expires_at", "is_expired_display")}),
        ("状态", {"fields": ("is_active",)}),
        (
            "时间信息",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["extend_license_30_days", "extend_license_365_days", "send_license_emails"]

    def is_expired_display(self, obj):
        """过期状态显示"""
        if obj.is_expired:
            return format_html('<span style="color: red;">已过期</span>')
        else:
            return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '过期状态'

    # 添加方法直接显示plan的原始值
    def plan(self, obj):
        return obj.plan
    plan.short_description = '许可计划'

    def extend_license_30_days(self, request, queryset):
        """延长许可证30天"""
        count = 0
        for license_obj in queryset:
            license_obj.extend_license(30)
            count += 1
        self.message_user(request, f"成功延长 {count} 个许可证 30 天")

    extend_license_30_days.short_description = "延长许可证30天"

    def extend_license_365_days(self, request, queryset):
        """延长许可证365天"""
        count = 0
        for license_obj in queryset:
            license_obj.extend_license(365)
            count += 1
        self.message_user(request, f"成功延长 {count} 个许可证 365 天")

    extend_license_365_days.short_description = "延长许可证365天"

    def send_license_emails(self, request, queryset):
        """发送许可证邮件"""
        success_count = 0
        for license_obj in queryset:
            if license_obj.send_license_email():
                success_count += 1
        self.message_user(request, f"成功发送 {success_count} 封许可证邮件")

    send_license_emails.short_description = "发送许可证邮件"


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = [
        "out_trade_no",
        "user_id",
        "sku_id",
        "total_amount",
        "status",
        "processed",
        "created_at",
    ]
    list_filter = ["status", "processed", "created_at"]
    search_fields = ["out_trade_no", "user_id", "sku_id", "remark"]
    readonly_fields = ["created_at", "updated_at"]
    list_editable = ["processed"]

    fieldsets = (
        ("订单信息", {"fields": ("out_trade_no", "user_id", "plan_id")}),
        ("商品信息", {"fields": ("sku_id", "month", "total_amount")}),
        ("状态", {"fields": ("status", "processed")}),
        ("备注", {"fields": ("remark",)}),
        (
            "时间信息",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    actions = ["process_orders", "mark_as_processed"]

    def process_orders(self, request, queryset):
        """处理订单"""
        success_count = 0
        for order in queryset.filter(status=2, processed=False):
            if order.process_order():
                success_count += 1
        self.message_user(request, f"成功处理 {success_count} 个订单")

    process_orders.short_description = "处理选中的订单"

    def mark_as_processed(self, request, queryset):
        """标记为已处理"""
        count = queryset.update(processed=True)
        self.message_user(request, f"成功标记 {count} 个订单为已处理")

    mark_as_processed.short_description = "标记为已处理"


# 自定义管理后台标题
admin.site.site_header = settings.ADMIN_SITE_HEADER
admin.site.site_title =  settings.ADMIN_SITE_TITLE
admin.site.index_title = settings.ADMIN_INDEX_TITLE
