# Pro 许可设计

概括：
- 分客户端、服务端，客户端启动时向服务端请求效验和更新许可信息
- 订阅制，许可信息中含到期时间
- 以AES加密交换许可信息，双方持有预置密钥 `secret`
- 以RSA签名验证许可信息，服务端持有私钥、客户端持有公钥

## 流程

### 在线验证

提供一个方法，供客户端启动时调用。

读取环境变量 `LICENSE` 它是一串AES加密后的字符串（用户从外部获得）。

LICENSE 用 `secret` 解密后格式如下：

```json
{
"license": "AES加密后的字符串",
"signature": "由服务端RAS签发"
}
```

客户端向服务器尝试发送在线授权请求，客户端请求格式：

```json
{
"license": "AES加密后的字符串",
"version": "0.0.1",
"timestamp": **********,
"signature": "md5字符串"
}
```

此处 `signature = md5("a=1&b=2&...按key排序拼接参数&secret")` ，区别于由服务端RAS签发的signature

服务端收到后；
1. 用同样的逻辑验证 `signature`
2. 如通过，检查 `timestamp` 是否在过去120秒内
3. 如通过，用 `secret` AES解密 `license` ，得到许可信息的原JSON格式：

```json
{
"app": "smartstrm",
"email": "xxx",
"plan": "pro",
"expires_at": **********,
"updated_at": **********,
}
```

其中：

- `app`: 应用标识
- `email` 唯一标识
- `expires_at` 授权过期时间
- `updated_at` 授权更新时间

服务端以 `email` 判断用户是否续订，更新 `expires_at` `updated_at` 字段，将JSON数据使用AES加密后返回：

```json
{
"code": 200,
"license": "AES加密后的字符串",
"signature": "对license进行RSA签名"
}
```


### 许可判断


客户端使用公钥验证签名，并用 `secret` AES解密在线许可 `license` ，比较 `expires_at` 时间小于当前时间，则认为授权已过期。

如在线验证许可请求失败，则使用原环境变量的许可，如果 `updated_at` 时间大于当前3天，则认为授权已过期。


## 许可应用

客户端在UI上显示授权信息：

- `email`
- 格式化后的 `expires_at`

在变量中保存许可信息，并提供一个方法以供其他模块判断某些功能是否能使用。