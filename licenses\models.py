from django.db import models
from django.utils import timezone


class Setting(models.Model):
    """系统设置"""

    key = models.CharField(max_length=100, unique=True, verbose_name="配置键")
    value = models.TextField(verbose_name="配置值")
    description = models.CharField(max_length=200, blank=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "系统设置"
        verbose_name_plural = "系统设置"

    def __str__(self):
        return f"{self.key}: {self.value[:50]}"

    @classmethod
    def get_value(cls, key, default=None):
        """获取设置值"""
        try:
            setting = cls.objects.get(key=key)
            return setting.value
        except cls.DoesNotExist:
            return default

    @classmethod
    def set_value(cls, key, value, description=""):
        """设置值"""
        setting, created = cls.objects.get_or_create(
            key=key, defaults={"value": value, "description": description}
        )
        if not created:
            setting.value = value
            if description:
                setting.description = description
            setting.save()
        return setting


class SKU(models.Model):
    """商品SKU"""

    sku_id = models.CharField(max_length=100, unique=True, verbose_name="SKU ID")
    app = models.CharField(max_length=50, verbose_name="应用名称")
    renewal_days = models.IntegerField(verbose_name="续订天数")
    name = models.CharField(max_length=200, verbose_name="商品名称")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "商品SKU"
        verbose_name_plural = "商品SKU"

    def __str__(self):
        return f"{self.name} ({self.app}) - {self.renewal_days}天"


class License(models.Model):
    """许可证"""

    PLAN_CHOICES = [
        ("basic", "basic"),
        ("pro", "pro"),
        ("enterprise", "enterprise"),
    ]

    app = models.CharField(max_length=50, verbose_name="应用名称")
    email = models.EmailField(verbose_name="用户邮箱")
    expires_at = models.DateTimeField(verbose_name="过期时间")
    plan = models.CharField(
        max_length=20, choices=PLAN_CHOICES, default="pro", verbose_name="许可计划"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "许可证"
        verbose_name_plural = "许可证"
        unique_together = ["app", "email"]

    def __str__(self):
        return f"{self.email} ({self.app}) - {self.plan}"

    @property
    def is_expired(self):
        """是否过期"""
        if self.expires_at is None:
            return False
        return timezone.now() > self.expires_at

    def extend_license(self, days):
        """延长许可证"""
        if self.is_expired:
            # 如果已过期，从当前时间开始计算
            self.expires_at = timezone.now() + timezone.timedelta(days=days)
        else:
            # 如果未过期，从过期时间开始延长
            self.expires_at += timezone.timedelta(days=days)
        self.save()

    def generate_license_data(self):
        """生成许可证数据"""
        from .utils import generate_license_data

        return generate_license_data(self)

    def generate_encrypted_license(self):
        """生成加密的许可证"""
        from .utils import generate_encrypted_license

        return generate_encrypted_license(self)

    def send_license_email(self):
        """发送许可证邮件"""
        from .utils import send_license_email

        return send_license_email(self)


class Order(models.Model):
    """订单"""

    STATUS_CHOICES = [
        (1, "待支付"),
        (2, "已支付"),
        (3, "已取消"),
        (4, "已退款"),
    ]

    out_trade_no = models.CharField(
        max_length=100, unique=True, verbose_name="外部订单号"
    )
    user_id = models.CharField(max_length=100, verbose_name="用户ID")
    plan_id = models.CharField(max_length=100, verbose_name="计划ID")
    sku_id = models.CharField(max_length=100, verbose_name="SKU ID")
    total_amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name="总金额"
    )
    status = models.IntegerField(
        choices=STATUS_CHOICES, default=1, verbose_name="订单状态"
    )
    remark = models.TextField(blank=True, verbose_name="备注(用户邮箱)")
    month = models.IntegerField(default=1, verbose_name="购买月数")
    processed = models.BooleanField(default=False, verbose_name="是否已处理")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "订单"
        verbose_name_plural = "订单"

    def __str__(self):
        return f"订单 {self.out_trade_no} - {self.get_status_display()}"

    def process_order(self):
        """处理订单"""
        if self.processed or self.status != 2:
            return False

        try:
            # 获取SKU信息
            sku = SKU.objects.get(sku_id=self.sku_id)

            # 计算续订天数
            renewal_days = sku.renewal_days * self.month

            # 获取或创建许可证
            email = self.remark  # 用户邮箱在remark字段中
            license_obj, created = License.objects.get_or_create(
                app=sku.app,
                email=email,
                defaults={
                    "expires_at": timezone.now()
                    + timezone.timedelta(days=renewal_days),
                    "plan": "pro",
                },
            )

            if not created:
                # 延长现有许可证
                license_obj.extend_license(renewal_days)

            # 发送许可证邮件
            license_obj.send_license_email()

            # 标记订单为已处理
            self.processed = True
            self.save()

            return True
        except Exception as e:
            print(f"处理订单失败: {e}")
            return False
