#!/usr/bin/env python3
"""
生成RSA密钥对用于许可系统
"""

from Crypto.PublicKey import RSA

def generate_rsa_keys():
    """生成RSA密钥对"""
    # 生成2048位RSA密钥
    key = RSA.generate(2048)

    # 获取私钥
    private_key = key.export_key()

    # 获取公钥
    public_key = key.publickey().export_key()

    print("=== RSA私钥 (服务端使用) ===")
    print(private_key.decode())
    print()

    print("=== RSA公钥 (客户端使用) ===")
    print(public_key.decode())
    print()

    return private_key.decode(), public_key.decode()

if __name__ == "__main__":
    generate_rsa_keys()
