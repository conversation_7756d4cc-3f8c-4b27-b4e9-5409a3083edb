"""
Django管理命令：生成许可证
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from licenses.models import License


class Command(BaseCommand):
    help = '生成许可证'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='用户邮箱')
        parser.add_argument('--app', type=str, default='smartstrm', help='应用名称')
        parser.add_argument('--days', type=int, default=30, help='有效天数')
        parser.add_argument('--plan', type=str, default='pro', help='许可计划')
        parser.add_argument('--send-email', action='store_true', help='发送邮件')

    def handle(self, *args, **options):
        email = options['email']
        app = options['app']
        days = options['days']
        plan = options['plan']
        send_email = options['send_email']

        # 获取或创建许可证
        license_obj, created = License.objects.get_or_create(
            app=app,
            email=email,
            defaults={
                'expires_at': timezone.now() + timezone.timedelta(days=days),
                'plan': plan
            }
        )

        if not created:
            # 延长现有许可证
            license_obj.extend_license(days)
            self.stdout.write(f"许可证已延长 {days} 天")
        else:
            self.stdout.write("新许可证已创建")

        # 生成许可证字符串
        license_env = license_obj.generate_encrypted_license()

        self.stdout.write(self.style.SUCCESS(f"许可证生成成功:"))
        self.stdout.write(f"用户: {email}")
        self.stdout.write(f"应用: {app}")
        self.stdout.write(f"计划: {plan}")
        self.stdout.write(f"过期时间: {license_obj.expires_at}")
        self.stdout.write(f"\nLICENSE环境变量:")
        self.stdout.write(f"export LICENSE='{license_env}'")

        # 发送邮件
        if send_email:
            if license_obj.send_license_email():
                self.stdout.write(self.style.SUCCESS("邮件发送成功"))
            else:
                self.stdout.write(self.style.ERROR("邮件发送失败"))
