#!/usr/bin/env python3
"""
SmartStrm Pro 许可服务器演示
单文件许可验证服务器，用于演示许可验证流程
"""

import json
import time
import hashlib
import base64
from typing import Dict, Any, Optional
from flask import Flask, request, jsonify
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad

app = Flask(__name__)

# 配置
SECRET_KEY = "SmartStrm2024SecretKey1234567890AB"  # 32字节密钥，与客户端保持一致
APP_NAME = "smartstrm"

# RSA密钥对（演示用，生产环境应该安全存储）
PRIVATE_KEY_PEM = """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

PUBLIC_KEY_PEM = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw3kabiWhjsgWJBZlnn8y
OIhbl5gzve2zptHSV2XOsFkVuCj1FX/PqNABFRq81rTBlG6Qb5I6BS+LK/s5NH+1
jIIcTs9Jj3NjSPUi7MV112pS3JnRsSIPv/oDYZI+qEscDNhIKsZ0Qe73HlI7i7FB
hErRxNTx2ELSgBKGHQHHgbUcCSs6ZPv76siPLfapgYItWmb5rOM8JnH9+Mr3Z3RF
incuuy1PCzb776Zqr2fpKBptn/Hk6bvdYtaGvqxaCzzqEOYokQzpBkjKPG2sh7u4
r8qA6aTIYq+h5TtjpizlPC0rcyNVwCJ7cg1XNCGeJ2K/j5yOLBf1px3nZKh5ywOp
dQIDAQAB
-----END PUBLIC KEY-----"""

# 模拟用户数据库
USERS_DB = {
    "<EMAIL>": {
        "email": "<EMAIL>",
        "expires_at": int(time.time()) + 30 * 24 * 3600,  # 30天后过期
        "plan": "pro"
    },
    "<EMAIL>": {
        "email": "<EMAIL>",
        "expires_at": int(time.time()) + 365 * 24 * 3600,  # 1年后过期
        "plan": "enterprise"
    }
}


def aes_encrypt(data: str) -> str:
    """AES加密"""
    try:
        cipher = AES.new(SECRET_KEY.encode()[:32], AES.MODE_CBC)
        padded_data = pad(data.encode(), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        # 返回 IV + 加密数据的base64编码
        return base64.b64encode(cipher.iv + encrypted).decode()
    except Exception as e:
        print(f"AES加密失败: {e}")
        return ""


def aes_decrypt(encrypted_data: str) -> str:
    """AES解密"""
    try:
        data = base64.b64decode(encrypted_data)
        iv = data[:16]  # AES block size
        encrypted = data[16:]
        cipher = AES.new(SECRET_KEY.encode()[:32], AES.MODE_CBC, iv)
        decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
        return decrypted.decode()
    except Exception as e:
        print(f"AES解密失败: {e}")
        return ""


def rsa_sign(data: str) -> str:
    """RSA签名"""
    try:
        private_key = RSA.import_key(PRIVATE_KEY_PEM)
        hash_obj = SHA256.new(data.encode())
        signature = pkcs1_15.new(private_key).sign(hash_obj)
        return base64.b64encode(signature).decode()
    except Exception as e:
        print(f"RSA签名失败: {e}")
        return ""


def verify_request_signature(params: Dict[str, Any], signature: str) -> bool:
    """验证请求签名"""
    try:
        # 移除signature参数
        params_copy = {k: v for k, v in params.items() if k != "signature"}

        # 按key排序拼接参数
        sorted_params = sorted(params_copy.items())
        param_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        param_str += f"&{SECRET_KEY}"

        expected_signature = hashlib.md5(param_str.encode()).hexdigest()
        return signature == expected_signature
    except Exception as e:
        print(f"验证请求签名失败: {e}")
        return False


@app.route("/api/license/verify", methods=["POST"])
def verify_license():
    """许可验证接口"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 400, "message": "缺少请求数据"}), 400

        # 验证请求签名
        signature = data.get("signature", "")
        if not verify_request_signature(data, signature):
            return jsonify({"code": 401, "message": "请求签名验证失败"}), 401

        # 验证时间戳（120秒内）
        timestamp = data.get("timestamp", 0)
        current_time = int(time.time())
        if abs(current_time - timestamp) > 120:
            return jsonify({"code": 401, "message": "请求时间戳无效"}), 401

        # 解密许可信息
        encrypted_license = data.get("license", "")
        license_json = aes_decrypt(encrypted_license)
        if not license_json:
            return jsonify({"code": 400, "message": "许可解密失败"}), 400

        license_data = json.loads(license_json)

        # 验证应用标识
        if license_data.get("app") != APP_NAME:
            return jsonify({"code": 400, "message": "应用标识不匹配"}), 400

        # 查找用户
        email = license_data.get("email", "")
        user = USERS_DB.get(email)
        if not user:
            return jsonify({"code": 404, "message": "用户不存在"}), 404

        # 更新许可信息
        updated_license = {
            "app": APP_NAME,
            "email": email,
            "expires_at": user["expires_at"],
            "updated_at": current_time,
            "plan": user.get("plan", "basic")
        }

        # 加密更新后的许可
        updated_license_json = json.dumps(updated_license)
        encrypted_updated_license = aes_encrypt(updated_license_json)

        # 生成RSA签名
        license_signature = rsa_sign(encrypted_updated_license)

        return jsonify({
            "code": 200,
            "message": "许可验证成功",
            "license": encrypted_updated_license,
            "signature": license_signature
        })

    except Exception as e:
        print(f"许可验证异常: {e}")
        return jsonify({"code": 500, "message": "服务器内部错误"}), 500


@app.route("/api/license/generate", methods=["POST"])
def generate_license():
    """生成许可（管理接口）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"code": 400, "message": "缺少请求数据"}), 400

        email = data.get("email", "")
        days = data.get("days", 30)
        plan = data.get("plan", "pro")

        if not email:
            return jsonify({"code": 400, "message": "缺少邮箱"}), 400

        # 创建许可信息
        current_time = int(time.time())
        license_data = {
            "app": APP_NAME,
            "email": email,
            "expires_at": current_time + days * 24 * 3600,
            "updated_at": current_time,
            "plan": plan
        }

        # 加密许可信息
        license_json = json.dumps(license_data)
        encrypted_license = aes_encrypt(license_json)

        # 生成RSA签名
        license_signature = rsa_sign(encrypted_license)

        # 创建最终的许可包
        license_package = {
            "license": encrypted_license,
            "signature": license_signature
        }

        # 加密许可包（这是给用户的LICENSE环境变量）
        package_json = json.dumps(license_package)
        final_license = aes_encrypt(package_json)

        # 更新用户数据库
        USERS_DB[email] = {
            "email": email,
            "expires_at": license_data["expires_at"],
            "plan": plan
        }

        return jsonify({
            "code": 200,
            "message": "许可生成成功",
            "license_env": final_license,
            "license_info": {
                "email": email,
                "expires_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(license_data["expires_at"])),
                "plan": plan
            }
        })

    except Exception as e:
        print(f"生成许可异常: {e}")
        return jsonify({"code": 500, "message": "服务器内部错误"}), 500


@app.route("/api/users", methods=["GET"])
def list_users():
    """列出所有用户（管理接口）"""
    users = []
    for email, user_data in USERS_DB.items():
        users.append({
            "email": email,
            "expires_at": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(user_data["expires_at"])),
            "plan": user_data.get("plan", "basic"),
            "is_expired": user_data["expires_at"] < int(time.time())
        })

    return jsonify({
        "code": 200,
        "users": users
    })


@app.route("/", methods=["GET"])
def index():
    """首页"""
    return """
    <h1>SmartStrm Pro 许可服务器演示</h1>
    <h2>API接口:</h2>
    <ul>
        <li><strong>POST /api/license/verify</strong> - 许可验证</li>
        <li><strong>POST /api/license/generate</strong> - 生成许可</li>
        <li><strong>GET /api/users</strong> - 列出用户</li>
    </ul>

    <h2>生成许可示例:</h2>
    <pre>
curl -X POST http://localhost:8099/api/license/generate \\
  -H "Content-Type: application/json" \\
  -d '{"email": "<EMAIL>", "days": 30, "plan": "pro"}'
    </pre>

    <h2>查看用户列表:</h2>
    <pre>
curl http://localhost:8099/api/users
    </pre>
    """


if __name__ == "__main__":
    print("SmartStrm Pro 许可服务器演示启动中...")
    print("服务器地址: http://localhost:8099")
    print("预置用户:")
    for email, user_data in USERS_DB.items():
        expires_str = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(user_data["expires_at"]))
        print(f"  - {email} (过期时间: {expires_str})")

    app.run(host="0.0.0.0", port=8099, debug=True)
