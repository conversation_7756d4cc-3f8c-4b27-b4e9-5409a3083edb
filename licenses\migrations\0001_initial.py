# Generated by Django 5.2.4 on 2025-08-04 20:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('out_trade_no', models.CharField(max_length=100, unique=True, verbose_name='外部订单号')),
                ('user_id', models.CharField(max_length=100, verbose_name='用户ID')),
                ('plan_id', models.CharField(max_length=100, verbose_name='计划ID')),
                ('sku_id', models.CharField(max_length=100, verbose_name='SKU ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='总金额')),
                ('status', models.IntegerField(choices=[(1, '待支付'), (2, '已支付'), (3, '已取消'), (4, '已退款')], default=1, verbose_name='订单状态')),
                ('remark', models.TextField(blank=True, verbose_name='备注(用户邮箱)')),
                ('month', models.IntegerField(default=1, verbose_name='购买月数')),
                ('processed', models.BooleanField(default=False, verbose_name='是否已处理')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
            },
        ),
        migrations.CreateModel(
            name='Setting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('description', models.CharField(blank=True, max_length=200, verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
            },
        ),
        migrations.CreateModel(
            name='SKU',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sku_id', models.CharField(max_length=100, unique=True, verbose_name='SKU ID')),
                ('app', models.CharField(max_length=50, verbose_name='应用名称')),
                ('renewal_days', models.IntegerField(verbose_name='续订天数')),
                ('name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '商品SKU',
                'verbose_name_plural': '商品SKU',
            },
        ),
        migrations.CreateModel(
            name='License',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app', models.CharField(max_length=50, verbose_name='应用名称')),
                ('email', models.EmailField(max_length=254, verbose_name='用户邮箱')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('plan', models.CharField(choices=[('basic', 'basic'), ('pro', 'pro'), ('enterprise', 'enterprise')], default='pro', max_length=20, verbose_name='许可计划')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '许可证',
                'verbose_name_plural': '许可证',
                'unique_together': {('app', 'email')},
            },
        ),
    ]
