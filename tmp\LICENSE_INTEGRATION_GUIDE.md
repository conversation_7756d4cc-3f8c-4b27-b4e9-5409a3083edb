# SmartStrm 许可集成指南

本指南说明如何在SmartStrm应用中使用许可管理功能。

## 功能概述

许可管理已完全集成到SmartStrm应用中，提供以下功能：

### 1. 应用启动时许可验证
- 应用启动时自动验证许可
- 支持在线验证和离线模式
- 显示许可状态和用户信息

### 2. 前端许可信息显示
- 侧边栏底部显示许可状态
- 区分Pro许可和免费版
- 显示用户邮箱和过期时间

### 3. 功能权限控制
- 代理服务器功能需要Pro许可
- 无Pro许可时禁用相关设置
- 显示升级提示

## 使用方法

### 1. 设置许可环境变量

```bash
# 设置许可环境变量
export LICENSE='your_license_string_here'

# 可选：设置许可服务器地址
export LICENSE_SERVER_URL='http://your-license-server.com'
```

### 2. 启动应用

```bash
cd src
python app.py
```

启动时会看到许可验证信息：
```
正在验证许可...
✓ 许可验证成功 - 用户: <EMAIL>
  过期时间: 2024-12-31 23:59:59
```

### 3. 前端界面

访问 `http://localhost:8024` 查看前端界面：

- **侧边栏底部**: 显示许可状态
  - 绿色背景：Pro许可有效
  - 黄色背景：免费版或许可无效
  - 显示用户邮箱和过期时间

- **系统设置 -> 代理服务器**:
  - 有Pro许可：正常使用所有功能
  - 无Pro许可：显示升级提示，禁用设置

## API接口

### 获取许可信息

```http
GET /api/license
Authorization: Bearer <token>
```

响应：
```json
{
  "is_valid": true,
  "email": "<EMAIL>",
  "expires_at_formatted": "2024-12-31 23:59:59",
  "is_pro": true
}
```

## 功能限制

### Pro功能列表

以下功能需要Pro许可：

1. **代理服务器**
   - 启用/禁用代理服务器
   - 配置代理端口和目标地址
   - 代理服务器运行状态

### 免费版功能

免费版包含以下功能：
- 存储管理
- 任务管理
- 存储浏览
- 基本系统设置
- Webhook通知

## 许可状态说明

### 有效Pro许可
- 侧边栏显示绿色许可信息
- 所有功能可用
- 显示用户邮箱和过期时间

### 无效许可/免费版
- 侧边栏显示黄色警告
- Pro功能被禁用
- 显示"某些功能受限"提示

### 许可过期
- 自动降级为免费版
- Pro功能不可用
- 提示联系管理员

## 故障排除

### 1. 许可验证失败

**症状**: 启动时显示"许可验证失败"

**解决方法**:
- 检查LICENSE环境变量是否正确设置
- 确认许可服务器可访问
- 检查系统时间是否正确

### 2. 前端不显示许可信息

**症状**: 侧边栏不显示许可状态

**解决方法**:
- 检查浏览器控制台是否有错误
- 确认已登录系统
- 刷新页面重新加载数据

### 3. 代理服务器无法启用

**症状**: 代理服务器设置被禁用

**解决方法**:
- 确认拥有有效的Pro许可
- 检查许可是否过期
- 联系管理员获取Pro许可

## 开发说明

### 添加新的Pro功能

1. **后端检查**:
```python
if not license_manager.is_feature_enabled("pro"):
    return jsonify({"message": "此功能需要Pro许可"}), 403
```

2. **前端限制**:
```html
<button :disabled="!license.is_pro">Pro功能</button>
```

3. **前端提示**:
```html
<div v-if="!license.is_pro" class="alert alert-warning">
    此功能需要Pro许可
</div>
```

### 许可信息获取

```javascript
// 前端获取许可信息
const licenseResponse = await axios.get('/api/license');
this.license = licenseResponse.data;
```

```python
# 后端获取许可信息
email = license_manager.get_email()
expires_at = license_manager.get_expires_at_formatted()
is_pro = license_manager.is_feature_enabled("pro")
```

## 许可服务器

许可服务器相关文档请参考 `license_server/` 目录中的文档。

### 生成测试许可

```bash
cd license_server
python license_tool.<NAME_EMAIL> --days 30 --plan pro
```

### 启动许可服务器

```bash
cd license_server
python license_server_demo.py
```

## 注意事项

1. **环境变量**: LICENSE环境变量必须在应用启动前设置
2. **网络连接**: 在线验证需要能访问许可服务器
3. **时间同步**: 确保系统时间正确，避免时间戳验证失败
4. **安全性**: 生产环境请使用HTTPS和安全的许可服务器

## 更新日志

- **v1.0.0**: 初始版本，支持基本许可验证和Pro功能控制
- 应用启动时许可验证
- 前端许可信息显示
- 代理服务器Pro功能限制
