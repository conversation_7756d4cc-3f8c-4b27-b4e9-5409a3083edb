"""
Django settings for license_server project.
"""

import os
from pathlib import Path
from dotenv import load_dotenv
from licenses.models import Setting

# Load environment variables
load_dotenv()


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


def get_config(key, default=None):
    """
    获取配置值，优先从数据库读取，如果数据库中没有则从环境变量读取
    当default是bool类型时，自动转换为布尔值
    """
    try:
        # 尝试从数据库的Setting表读取
        value = Setting.get_value(key)
        if value is not None:
            if isinstance(default, bool):
                return value.lower() in ("true", "1", "yes", "on")
            return value
    except Exception:
        pass
    # 从环境变量读取
    value = os.environ.get(key, default)
    if isinstance(default, bool):
        return value.lower() in ("true", "1", "yes", "on")
    return value


def get_license_rsa_private_key():
    """
    获取RSA私钥，支持文件、数据库和环境变量三种方式
    """
    # 先检查是否有文件路径配置
    key_file_path = get_config("LICENSE_RSA_PRIVATE_KEY_FILE")
    if key_file_path and os.path.exists(key_file_path):
        try:
            with open(key_file_path, "r") as f:
                return f.read()
        except Exception as e:
            print(f"读取RSA私钥文件失败: {e}")
    # 检查是否在数据库中直接存储了密钥
    return get_config("LICENSE_RSA_PRIVATE_KEY", "")

API_TOKEN = get_config("API_TOKEN", "")

# SECURITY WARNING: keep the secret key used in production secret!
# 如果没有配置SECRET_KEY，则使用默认值
SECRET_KEY = get_config("SECRET_KEY", "django-insecure-change-me-in-production")

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_config("DEBUG", False)

# 主机配置
_allowed_hosts = get_config("ALLOWED_HOSTS", "localhost,127.0.0.1")
ALLOWED_HOSTS = [host.strip() for host in _allowed_hosts.split(",")]

# Admin URL path
# 如果没有配置ADMIN_URL，则使用默认值'admin'
ADMIN_URL = get_config("ADMIN_URL", "admin")

# License server specific settings
# 如果没有配置LICENSE_SECRET_KEY，则使用默认值
LICENSE_SECRET_KEY = get_config(
    "LICENSE_SECRET_KEY", "SmartStrm2024SecretKey1234567890AB"
)

LICENSE_RSA_PRIVATE_KEY = get_license_rsa_private_key()

# Email settings
EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = get_config("EMAIL_HOST", "smtp.gmail.com")
EMAIL_PORT = int(get_config("EMAIL_PORT", "587"))
EMAIL_USE_TLS = get_config("EMAIL_USE_TLS", True)
EMAIL_HOST_USER = get_config("EMAIL_HOST_USER", "")
EMAIL_HOST_PASSWORD = get_config("EMAIL_HOST_PASSWORD", "")
DEFAULT_FROM_EMAIL = get_config("DEFAULT_FROM_EMAIL", "<EMAIL>")

# Admin site settings
ADMIN_SITE_HEADER = get_config("ADMIN_SITE_HEADER", "许可证管理系统")
ADMIN_SITE_TITLE = get_config("ADMIN_SITE_TITLE", "许可证管理系统")
ADMIN_INDEX_TITLE = get_config("ADMIN_INDEX_TITLE", "欢迎使用许可证管理系统")

# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "licenses",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "license_server.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

# Logging
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": BASE_DIR / "log" / "license_server.log",
        },
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "django": {
            "handlers": ["file", "console"],
            "level": "INFO",
            "propagate": True,
        },
        "licenses": {
            "handlers": ["file", "console"],
            "level": "INFO",
            "propagate": True,
        },
    },
}


WSGI_APPLICATION = "license_server.wsgi.application"

# Database
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": BASE_DIR / "db" / "db.sqlite3",
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
LANGUAGE_CODE = "zh-hans"
TIME_ZONE = "Asia/Shanghai"
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"

# Default primary key field type
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"
