# SmartStrm Django 许可证服务器

基于Django的完整许可证管理系统，支持后台管理、自动化处理和邮件通知。

## 功能特性

### 🎯 **核心功能**
- **许可证管理**: 完整的许可证生命周期管理
- **后台管理**: Django Admin 可视化管理界面
- **自动化处理**: 支付回调自动处理订单
- **邮件通知**: 自动发送许可证到用户邮箱
- **API接口**: HTTP API支持客户端验证

### 📊 **数据模型**
1. **License**: 许可证表 (app, email, expires_at, plan, updated_at)
2. **SKU**: 商品表 (sku_id, app, renewal_days)
3. **Order**: 订单表 (user_id, plan_id, sku_id, total_amount, remark)
4. **Setting**: 系统设置表 (key, value)

### 🔗 **API接口**
- `POST /api/license/verify` - 许可证验证
- `POST /api/license/generate` - 生成许可证
- `GET /api/users` - 用户列表
- `POST /api/webhook/afdian` - 爱发电支付回调

## 快速开始

### 1. 安装依赖

```bash
cd license_server
pip install -r requirements.txt
```

### 2. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 3. 启动服务器

```bash
# 使用启动脚本（推荐）
python start.py

# 或手动启动
python manage.py makemigrations
python manage.py migrate
python manage.py createsuperuser
python manage.py runserver 0.0.0.0:8099
```

### 4. 访问管理后台

- 地址: http://localhost:8099/admin/
- 用户名: admin
- 密码: admin123

## 数据库设计

### License 许可证表
```sql
CREATE TABLE licenses_license (
    id INTEGER PRIMARY KEY,
    app VARCHAR(50) NOT NULL,
    email VARCHAR(254) NOT NULL,
    expires_at DATETIME NOT NULL,
    plan VARCHAR(20) DEFAULT 'pro',
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    UNIQUE(app, email)
);
```

### SKU 商品表
```sql
CREATE TABLE licenses_sku (
    id INTEGER PRIMARY KEY,
    sku_id VARCHAR(100) UNIQUE NOT NULL,
    app VARCHAR(50) NOT NULL,
    renewal_days INTEGER NOT NULL,
    name VARCHAR(200) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### Order 订单表
```sql
CREATE TABLE licenses_order (
    id INTEGER PRIMARY KEY,
    out_trade_no VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(100) NOT NULL,
    plan_id VARCHAR(100) NOT NULL,
    sku_id VARCHAR(100) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status INTEGER DEFAULT 1,
    remark TEXT,
    month INTEGER DEFAULT 1,
    processed BOOLEAN DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### Setting 系统设置表
```sql
CREATE TABLE licenses_setting (
    id INTEGER PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description VARCHAR(200),
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 爱发电集成

### Webhook配置

在爱发电后台配置Webhook URL:
```
http://your-domain.com/api/webhook/afdian
```

### SKU配置

在管理后台创建对应的SKU:
```python
SKU.objects.create(
    sku_id='b082342c4aba11ebb5cb52540025c377',  # 爱发电的SKU ID
    app='smartstrm',
    renewal_days=30,
    name='SmartStrm Pro 30天',
    price=29.99
)
```

### 订单处理流程

1. 用户在爱发电完成支付
2. 爱发电发送Webhook到服务器
3. 服务器验证订单并创建Order记录
4. 自动生成/延长许可证
5. 发送许可证邮件给用户

## 管理后台功能

### 许可证管理
- 查看所有许可证
- 批量延长许可证
- 发送许可证邮件
- 过期状态显示

### 订单管理
- 查看所有订单
- 手动处理订单
- 订单状态管理

### SKU管理
- 商品配置管理
- 价格和天数设置
- 启用/禁用商品

### 系统设置
- SMTP邮件配置
- 系统参数管理

## API使用示例

### 验证许可证

```bash
curl -X POST http://localhost:8099/api/license/verify \
  -H "Content-Type: application/json" \
  -d '{
    "license": "encrypted_license_data",
    "version": "1.0.0",
    "timestamp": **********,
    "signature": "md5_signature"
  }'
```

### 生成许可证

```bash
curl -X POST http://localhost:8099/api/license/generate \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "days": 30,
    "plan": "pro",
    "app": "smartstrm"
  }'
```

### 获取用户列表

```bash
curl http://localhost:8099/api/users
```

## 邮件配置

### Gmail配置示例

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

### 邮件模板

系统会自动发送包含许可证的邮件:
```
主题: SmartStrm 许可证更新通知

内容:
- 用户邮箱
- 许可计划
- 过期时间
- LICENSE环境变量
- 设置说明
```

## 部署说明

### 生产环境配置

1. **安全设置**:
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=your-domain.com
```

2. **数据库**: 建议使用PostgreSQL或MySQL
3. **静态文件**: 配置nginx服务静态文件
4. **HTTPS**: 使用SSL证书保护API

### Docker部署

```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8099
CMD ["python", "start.py"]
```

## 故障排除

### 常见问题

1. **邮件发送失败**:
   - 检查SMTP配置
   - 确认邮箱密码正确
   - 检查防火墙设置

2. **Webhook接收失败**:
   - 检查URL配置
   - 查看服务器日志
   - 验证SKU是否存在

3. **许可证验证失败**:
   - 检查密钥配置
   - 确认时间戳正确
   - 验证签名算法

### 日志查看

```bash
# 查看Django日志
tail -f license_server.log

# 查看数据库
python manage.py dbshell
```

## 开发指南

### 添加新功能

1. 修改模型: `licenses/models.py`
2. 创建迁移: `python manage.py makemigrations`
3. 应用迁移: `python manage.py migrate`
4. 更新管理后台: `licenses/admin.py`
5. 添加API: `licenses/views.py`

### 测试

```bash
# 运行测试
python manage.py test

# 创建测试数据
python manage.py shell
>>> from licenses.models import *
>>> # 创建测试数据
```

## 许可证

本项目基于MIT许可证开源。
