#!/usr/bin/env python3
"""
测试Django许可证服务器
"""

import requests
import json
import time

def test_server():
    """测试服务器功能"""
    base_url = "http://localhost:8099"
    
    print("Django许可证服务器测试")
    print("=" * 50)
    
    # 测试首页
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            data = response.json()
            print(f"  版本: {data.get('version')}")
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务器: {e}")
        return False
    
    # 测试生成许可证
    print("\n测试生成许可证...")
    try:
        generate_data = {
            "email": "<EMAIL>",
            "days": 30,
            "plan": "pro",
            "app": "smartstrm"
        }
        
        response = requests.post(f"{base_url}/api/license/generate", json=generate_data)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                print("✓ 许可证生成成功")
                license_env = result.get("license_env")
                print(f"  许可证长度: {len(license_env)} 字符")
                return license_env
            else:
                print(f"✗ 许可证生成失败: {result.get('message')}")
        else:
            print(f"✗ 生成请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 生成许可证异常: {e}")
    
    return None

def test_webhook():
    """测试Webhook接口"""
    base_url = "http://localhost:8099"
    
    print("\n测试Webhook接口...")
    
    # 模拟爱发电Webhook数据
    webhook_data = {
        "ec": 200,
        "em": "ok",
        "data": {
            "type": "order",
            "order": {
                "out_trade_no": f"test_{int(time.time())}",
                "custom_order_id": "Steam12345",
                "user_id": "test_user_123",
                "user_private_id": "test_private_123",
                "plan_id": "test_plan_123",
                "month": 1,
                "total_amount": "29.99",
                "show_amount": "29.99",
                "status": 2,
                "remark": "<EMAIL>",
                "redeem_id": "",
                "product_type": 0,
                "discount": "0.00",
                "sku_detail": [{
                    "sku_id": "smartstrm_pro_30",
                    "count": 1,
                    "name": "SmartStrm Pro 30天",
                    "album_id": "",
                    "pic": ""
                }],
                "address_person": "",
                "address_phone": "",
                "address_address": ""
            }
        }
    }
    
    try:
        response = requests.post(f"{base_url}/api/webhook/afdian", json=webhook_data)
        if response.status_code == 200:
            result = response.json()
            if result.get("ec") == 200:
                print("✓ Webhook处理成功")
                print(f"  响应: {result.get('em')}")
            else:
                print(f"✗ Webhook处理失败: {result.get('em')}")
        else:
            print(f"✗ Webhook请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ Webhook测试异常: {e}")

def test_users_api():
    """测试用户列表API"""
    base_url = "http://localhost:8099"
    
    print("\n测试用户列表API...")
    
    try:
        response = requests.get(f"{base_url}/api/users")
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                users = result.get("users", [])
                print(f"✓ 获取用户列表成功，共 {len(users)} 个用户")
                for user in users[:3]:  # 显示前3个用户
                    print(f"  - {user['email']} ({user['app']}) - {user['plan']}")
            else:
                print(f"✗ 获取用户列表失败: {result.get('message')}")
        else:
            print(f"✗ 用户列表请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 用户列表测试异常: {e}")

def main():
    """主函数"""
    # 测试基本功能
    license_env = test_server()
    
    # 测试Webhook
    test_webhook()
    
    # 测试用户API
    test_users_api()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    if license_env:
        print(f"\n生成的测试许可证:")
        print(f"export LICENSE='{license_env}'")
    
    print("\n管理后台: http://localhost:8099/admin/")
    print("用户名: admin")
    print("密码: admin123")

if __name__ == "__main__":
    main()
