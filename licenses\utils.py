"""
许可证服务器工具函数
"""

import hashlib
import base64
import json
import time
from django.conf import settings
from django.core.mail import send_mail
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad


def aes_encrypt(data: str) -> str:
    """AES加密"""
    try:
        secret_key = settings.LICENSE_SECRET_KEY
        cipher = AES.new(secret_key.encode()[:32], AES.MODE_CBC)
        padded_data = pad(data.encode(), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        # 返回 IV + 加密数据的base64编码
        return base64.b64encode(cipher.iv + encrypted).decode()
    except Exception as e:
        print(f"AES加密失败: {e}")
        return ""


def aes_decrypt(encrypted_data: str) -> str:
    """AES解密"""
    try:
        secret_key = settings.LICENSE_SECRET_KEY
        data = base64.b64decode(encrypted_data)
        iv = data[:16]  # AES block size
        encrypted = data[16:]
        cipher = AES.new(secret_key.encode()[:32], AES.MODE_CBC, iv)
        decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
        return decrypted.decode()
    except Exception as e:
        print(f"AES解密失败: {e}")
        return ""


def rsa_sign(data: str) -> str:
    """RSA签名"""
    try:
        private_key = RSA.import_key(settings.LICENSE_RSA_PRIVATE_KEY)
        hash_obj = SHA256.new(data.encode())
        signature = pkcs1_15.new(private_key).sign(hash_obj)
        return base64.b64encode(signature).decode()
    except Exception as e:
        print(f"RSA签名失败: {e}")
        return ""


def verify_request_signature(params: dict, signature: str) -> bool:
    """验证请求签名"""
    try:
        # 移除signature参数
        params_copy = {k: v for k, v in params.items() if k != "signature"}

        # 按key排序拼接参数
        sorted_params = sorted(params_copy.items())
        param_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        param_str += f"&{settings.LICENSE_SECRET_KEY}"

        expected_signature = hashlib.md5(param_str.encode()).hexdigest()
        return signature == expected_signature
    except Exception as e:
        print(f"验证请求签名失败: {e}")
        return False


def generate_license_data(license_obj):
    """生成许可证数据"""
    current_time = int(time.time())
    license_data = {
        "app": license_obj.app,
        "email": license_obj.email,
        "expires_at": int(license_obj.expires_at.timestamp()),
        "updated_at": current_time,
        "plan": license_obj.plan,
    }
    return license_data


def generate_encrypted_license(license_obj):
    """生成加密的许可证"""
    license_data = generate_license_data(license_obj)

    # 加密许可数据
    license_json = json.dumps(license_data)
    encrypted_license = aes_encrypt(license_json)

    # 生成RSA签名
    license_signature = rsa_sign(encrypted_license)

    # 创建许可包
    license_package = {"license": encrypted_license, "signature": license_signature}

    # 加密许可包（这是最终的LICENSE环境变量）
    package_json = json.dumps(license_package)
    final_license = aes_encrypt(package_json)

    return final_license


def send_license_email(license_obj):
    """发送许可证邮件"""
    try:
        license_env = generate_encrypted_license(license_obj)

        subject = f"{license_obj.app} 许可证更新通知"
        message = f"""
亲爱的用户，

您的 {license_obj.app} 许可证已更新：

用户邮箱: {license_obj.email}
许可计划: {license_obj.get_plan_display()}
过期时间: {license_obj.expires_at.strftime('%Y-%m-%d %H:%M:%S')}

请将以下许可证字符串设置为环境变量 LICENSE：

{license_env}

Linux/Mac 设置方法：
export LICENSE='{license_env}'

Windows 设置方法：
set LICENSE={license_env}

感谢您的支持！

{license_obj.app} 团队
        """

        send_mail(
            subject=subject,
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[license_obj.email],
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"发送邮件失败: {e}")
        return False
