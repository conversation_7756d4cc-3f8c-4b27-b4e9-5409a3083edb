"""
许可证服务器工具函数
"""

import hashlib
import base64
from django.conf import settings
from Crypto.Cipher import AES
from Crypto.PublicKey import RSA
from Crypto.Signature import pkcs1_15
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad


def aes_encrypt(data: str) -> str:
    """AES加密"""
    try:
        secret_key = settings.LICENSE_SECRET_KEY
        cipher = AES.new(secret_key.encode()[:32], AES.MODE_CBC)
        padded_data = pad(data.encode(), AES.block_size)
        encrypted = cipher.encrypt(padded_data)
        # 返回 IV + 加密数据的base64编码
        return base64.b64encode(cipher.iv + encrypted).decode()
    except Exception as e:
        print(f"AES加密失败: {e}")
        return ""


def aes_decrypt(encrypted_data: str) -> str:
    """AES解密"""
    try:
        secret_key = settings.LICENSE_SECRET_KEY
        data = base64.b64decode(encrypted_data)
        iv = data[:16]  # AES block size
        encrypted = data[16:]
        cipher = AES.new(secret_key.encode()[:32], AES.MODE_CBC, iv)
        decrypted = unpad(cipher.decrypt(encrypted), AES.block_size)
        return decrypted.decode()
    except Exception as e:
        print(f"AES解密失败: {e}")
        return ""


def rsa_sign(data: str) -> str:
    """RSA签名"""
    try:
        private_key = RSA.import_key(settings.LICENSE_RSA_PRIVATE_KEY)
        hash_obj = SHA256.new(data.encode())
        signature = pkcs1_15.new(private_key).sign(hash_obj)
        return base64.b64encode(signature).decode()
    except Exception as e:
        print(f"RSA签名失败: {e}")
        return ""


def verify_request_signature(params: dict, signature: str) -> bool:
    """验证请求签名"""
    try:
        # 移除signature参数
        params_copy = {k: v for k, v in params.items() if k != "signature"}
        
        # 按key排序拼接参数
        sorted_params = sorted(params_copy.items())
        param_str = "&".join([f"{k}={v}" for k, v in sorted_params])
        param_str += f"&{settings.LICENSE_SECRET_KEY}"
        
        expected_signature = hashlib.md5(param_str.encode()).hexdigest()
        return signature == expected_signature
    except Exception as e:
        print(f"验证请求签名失败: {e}")
        return False
