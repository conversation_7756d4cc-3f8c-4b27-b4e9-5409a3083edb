import json
import time
import hashlib
import logging
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from django.conf import settings
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from .models import License, Order, SKU, Setting
from .utils import aes_encrypt, aes_decrypt, rsa_sign, verify_request_signature

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class IndexView(View):
    """首页"""
    def get(self, request):
        return JsonResponse({
            "message": "SmartStrm 许可证服务器",
            "version": "1.0.0",
            "endpoints": {
                "verify": "/api/license/verify",
                "generate": "/api/license/generate",
                "webhook": "/api/webhook/afdian",
                "admin": "/admin/"
            }
        })


@api_view(['POST'])
@permission_classes([AllowAny])
def verify_license(request):
    """许可证验证接口"""
    try:
        data = request.data
        if not data:
            return Response({"code": 400, "message": "缺少请求数据"}, status=400)

        # 验证请求签名
        signature = data.get("signature", "")
        if not verify_request_signature(data, signature):
            return Response({"code": 401, "message": "请求签名验证失败"}, status=401)

        # 验证时间戳（120秒内）
        timestamp = data.get("timestamp", 0)
        current_time = int(time.time())
        if abs(current_time - timestamp) > 120:
            return Response({"code": 401, "message": "请求时间戳无效"}, status=401)

        # 解密许可信息
        encrypted_license = data.get("license", "")
        license_json = aes_decrypt(encrypted_license)
        if not license_json:
            return Response({"code": 400, "message": "许可解密失败"}, status=400)

        license_data = json.loads(license_json)

        # 验证应用标识
        app_name = license_data.get("app", "")
        if not app_name:
            return Response({"code": 400, "message": "应用标识不能为空"}, status=400)

        # 查找许可证
        email = license_data.get("email", "")
        try:
            license_obj = License.objects.get(app=app_name, email=email, is_active=True)
        except License.DoesNotExist:
            return Response({"code": 404, "message": "许可证不存在"}, status=404)

        # 检查是否过期
        if license_obj.is_expired:
            return Response({"code": 403, "message": "许可证已过期"}, status=403)

        # 更新许可信息
        updated_license_data = license_obj.generate_license_data()

        # 加密更新后的许可
        updated_license_json = json.dumps(updated_license_data)
        encrypted_updated_license = aes_encrypt(updated_license_json)

        # 生成RSA签名
        license_signature = rsa_sign(encrypted_updated_license)

        logger.info(f"许可证验证成功: {email} ({app_name})")

        return Response({
            "code": 200,
            "message": "许可验证成功",
            "license": encrypted_updated_license,
            "signature": license_signature
        })

    except Exception as e:
        logger.error(f"许可验证异常: {e}")
        return Response({"code": 500, "message": "服务器内部错误"}, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def generate_license(request):
    """生成许可证接口（管理接口）"""
    try:
        data = request.data
        if not data:
            return Response({"code": 400, "message": "缺少请求数据"}, status=400)

        email = data.get("email", "")
        days = data.get("days", 30)
        plan = data.get("plan", "pro")
        app = data.get("app", "smartstrm")

        if not email:
            return Response({"code": 400, "message": "缺少邮箱"}, status=400)

        # 获取或创建许可证
        license_obj, created = License.objects.get_or_create(
            app=app,
            email=email,
            defaults={
                'expires_at': timezone.now() + timezone.timedelta(days=days),
                'plan': plan
            }
        )

        if not created:
            # 延长现有许可证
            license_obj.extend_license(days)

        # 生成加密许可证
        final_license = license_obj.generate_encrypted_license()

        logger.info(f"许可证生成成功: {email} ({app})")

        return Response({
            "code": 200,
            "message": "许可生成成功",
            "license_env": final_license,
            "license_info": {
                "email": email,
                "expires_at": license_obj.expires_at.strftime("%Y-%m-%d %H:%M:%S"),
                "plan": plan,
                "app": app
            }
        })

    except Exception as e:
        logger.error(f"生成许可异常: {e}")
        return Response({"code": 500, "message": "服务器内部错误"}, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def afdian_webhook(request):
    """爱发电Webhook接口"""
    try:
        # 解析JSON数据
        data = json.loads(request.body)

        # 验证基本结构
        if data.get("ec") != 200 or data.get("data", {}).get("type") != "order":
            logger.warning(f"无效的webhook数据: {data}")
            return JsonResponse({"ec": 400, "em": "无效的数据格式"})

        order_data = data["data"]["order"]

        # 检查订单状态（只处理已支付的订单）
        if order_data.get("status") != 2:
            logger.info(f"订单状态不是已支付，跳过处理: {order_data.get('out_trade_no')}")
            return JsonResponse({"ec": 200, "em": "订单状态不是已支付"})

        # 检查是否已存在该订单
        out_trade_no = order_data.get("out_trade_no")
        if Order.objects.filter(out_trade_no=out_trade_no).exists():
            logger.info(f"订单已存在，跳过处理: {out_trade_no}")
            return JsonResponse({"ec": 200, "em": "订单已存在"})

        # 获取SKU信息
        sku_detail = order_data.get("sku_detail", [])
        if not sku_detail:
            logger.warning(f"订单没有SKU信息: {out_trade_no}")
            return JsonResponse({"ec": 400, "em": "订单没有SKU信息"})

        sku_id = sku_detail[0].get("sku_id")

        # 验证SKU是否存在
        try:
            sku = SKU.objects.get(sku_id=sku_id, is_active=True)
        except SKU.DoesNotExist:
            logger.warning(f"SKU不存在或已禁用: {sku_id}")
            return JsonResponse({"ec": 400, "em": "SKU不存在或已禁用"})

        # 创建订单记录
        order = Order.objects.create(
            out_trade_no=out_trade_no,
            user_id=order_data.get("user_id", ""),
            plan_id=order_data.get("plan_id", ""),
            sku_id=sku_id,
            total_amount=order_data.get("total_amount", "0.00"),
            status=order_data.get("status", 2),
            remark=order_data.get("remark", ""),
            month=order_data.get("month", 1)
        )

        # 处理订单（生成/延长许可证并发送邮件）
        if order.process_order():
            logger.info(f"订单处理成功: {out_trade_no}")
            return JsonResponse({"ec": 200, "em": "订单处理成功"})
        else:
            logger.error(f"订单处理失败: {out_trade_no}")
            return JsonResponse({"ec": 500, "em": "订单处理失败"})

    except json.JSONDecodeError:
        logger.error("Webhook数据不是有效的JSON")
        return JsonResponse({"ec": 400, "em": "无效的JSON数据"})
    except Exception as e:
        logger.error(f"Webhook处理异常: {e}")
        return JsonResponse({"ec": 500, "em": "服务器内部错误"})


@api_view(['GET'])
@permission_classes([AllowAny])
def list_users(request):
    """列出所有用户（管理接口）"""
    try:
        licenses = License.objects.all().order_by('-created_at')
        users = []

        for license_obj in licenses:
            users.append({
                "email": license_obj.email,
                "app": license_obj.app,
                "expires_at": license_obj.expires_at.strftime("%Y-%m-%d %H:%M:%S"),
                "plan": license_obj.plan,
                "is_expired": license_obj.is_expired,
                "is_active": license_obj.is_active
            })

        return Response({
            "code": 200,
            "users": users
        })

    except Exception as e:
        logger.error(f"获取用户列表异常: {e}")
        return Response({"code": 500, "message": "服务器内部错误"}, status=500)
