# SmartStrm Pro 许可系统

基于 `tmp/pro.md` 设计的完整许可验证系统，支持在线验证和离线模式。

## 系统架构

### 组件说明

1. **许可管理器** (`src/core/license_manager.py`) - 客户端许可验证模块
2. **许可服务器** (`license_server_demo.py`) - 单文件许可验证服务器
3. **许可工具** (`license_tool.py`) - 命令行许可管理工具
4. **测试脚本** (`test_license_system.py`) - 完整系统测试

### 安全机制

- **AES加密**: 使用预置密钥加密许可信息传输
- **RSA签名**: 服务端私钥签名，客户端公钥验证
- **时间戳验证**: 防止重放攻击（120秒有效期）
- **MD5签名**: 请求参数完整性验证

## 快速开始

### 1. 安装依赖

```bash
pip install pycryptodome flask requests
```

### 2. 启动许可服务器

```bash
python license_server_demo.py
```

服务器将在 `http://localhost:8099` 启动，预置了两个测试用户：
- `<EMAIL>` (30天有效期)
- `<EMAIL>` (1年有效期)

### 3. 生成许可

```bash
# 为新用户生成30天许可
python license_tool.<NAME_EMAIL> --days 30 --plan pro

# 查看所有用户
python license_tool.py list
```

### 4. 使用许可

```bash
# 设置环境变量（从生成许可的输出中复制）
export LICENSE='生成的许可字符串'

# 验证许可
python license_tool.py verify
```

## 许可验证流程

### 在线验证

1. 客户端读取 `LICENSE` 环境变量
2. 解密获取许可信息和签名
3. 构建验证请求（包含时间戳和MD5签名）
4. 发送到许可服务器验证
5. 服务器验证签名、时间戳和用户信息
6. 返回更新的许可信息（RSA签名）
7. 客户端验证RSA签名并更新许可

### 离线验证

1. 在线验证失败时自动启用
2. 使用环境变量中的许可信息
3. 验证RSA签名确保完整性
4. 检查 `updated_at` 不超过3天

## API接口

### 许可验证 `POST /api/license/verify`

**请求格式:**
```json
{
  "license": "AES加密的许可信息",
  "version": "1.0.0",
  "timestamp": **********,
  "signature": "MD5签名"
}
```

**响应格式:**
```json
{
  "code": 200,
  "message": "许可验证成功",
  "license": "AES加密的更新许可",
  "signature": "RSA签名"
}
```

### 生成许可 `POST /api/license/generate`

**请求格式:**
```json
{
  "email": "<EMAIL>",
  "days": 30,
  "plan": "pro"
}
```

### 用户列表 `GET /api/users`

返回所有用户的许可状态信息。

## 许可信息格式

### 原始许可数据
```json
{
  "app": "smartstrm",
  "email": "<EMAIL>",
  "expires_at": **********,
  "updated_at": **********,
  "plan": "pro"
}
```

### 环境变量格式
```json
{
  "license": "AES加密的许可数据",
  "signature": "RSA签名"
}
```

## 集成到SmartStrm

### 1. 初始化许可管理器

```python
from core.license_manager import LicenseManager

# 创建许可管理器
license_manager = LicenseManager("http://your-license-server.com")

# 初始化许可验证
if license_manager.initialize():
    print("许可验证成功")
else:
    print("许可验证失败")
```

### 2. 功能权限检查

```python
# 检查Pro功能是否可用
if license_manager.is_feature_enabled("pro_feature"):
    # 执行Pro功能
    pass
else:
    # 显示升级提示
    pass
```

### 3. 显示许可信息

```python
# 获取许可信息
email = license_manager.get_email()
expires_at = license_manager.get_expires_at_formatted()

print(f"许可用户: {email}")
print(f"过期时间: {expires_at}")
```

## 测试

### 运行完整测试

```bash
python test_license_system.py
```

测试包括：
- 启动许可服务器
- 生成测试许可
- 在线验证测试
- 离线模式测试
- 用户列表查看

### 手动测试

```bash
# 启动服务器
python license_server_demo.py

# 在另一个终端生成许可
python license_tool.<NAME_EMAIL> --days 7

# 设置环境变量并验证
export LICENSE='生成的许可字符串'
python license_tool.py verify
```

## 生产部署注意事项

1. **密钥安全**: 
   - 更换默认的AES密钥
   - 安全存储RSA私钥
   - 使用环境变量或密钥管理服务

2. **服务器安全**:
   - 使用HTTPS
   - 添加认证和授权
   - 实现速率限制

3. **数据库**:
   - 替换内存数据库为持久化存储
   - 实现用户管理功能
   - 添加许可使用统计

4. **监控**:
   - 添加日志记录
   - 实现许可使用监控
   - 设置告警机制

## 故障排除

### 常见问题

1. **许可验证失败**
   - 检查LICENSE环境变量是否正确设置
   - 确认许可服务器可访问
   - 验证系统时间是否正确

2. **离线模式失效**
   - 检查许可是否超过3天未更新
   - 确认RSA签名验证通过

3. **服务器连接失败**
   - 检查网络连接
   - 确认服务器地址和端口
   - 查看防火墙设置

### 调试模式

在许可管理器中启用详细日志：

```python
import logging
logging.getLogger('core.license_manager').setLevel(logging.DEBUG)
```

## 许可证

本许可系统仅供演示使用，生产环境请根据实际需求进行安全加固。
