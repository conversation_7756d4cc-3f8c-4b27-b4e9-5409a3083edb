#!/usr/bin/env python3
"""
许可证服务器启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("许可证管理系统")
    print("=" * 50)

    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'license_server.settings')

    # 检查是否需要安装依赖
    try:
        import django
        from django.core.management import execute_from_command_line
    except ImportError:
        print("Django未安装，正在安装依赖...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
            import django
            from django.core.management import execute_from_command_line
        except subprocess.CalledProcessError:
            print("依赖安装失败，请手动安装:")
            print("pip install -r requirements.txt")
            return
        except ImportError:
            print("Django安装失败，请检查Python环境")
            return

    # 设置Django
    django.setup()

    # 检查数据库是否需要迁移
    db_file = Path("db/db.sqlite3")
    if not db_file.exists():
        print("初始化数据库...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        execute_from_command_line(['manage.py', 'migrate'])

        # 创建超级用户
        print("\n创建管理员账户...")
        print("用户名: admin")
        print("密码: admin123")
        print("邮箱: <EMAIL>")

        from django.contrib.auth.models import User
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            print("✓ 管理员账户创建成功")

        # 创建示例数据
        print("\n创建示例数据...")
        create_sample_data()

    print("\n启动服务器...")
    print("管理后台: http://localhost:8099/admin/")
    print("用户名: admin")
    print("密码: admin123")
    print("\nAPI接口:")
    print("- 许可验证: POST /api/license/verify")
    print("- 生成许可: POST /api/license/generate")
    print("- 用户列表: GET /api/users")
    print("- Webhook: POST /api/webhook/afdian")

    # 启动开发服务器
    execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8099'])

def create_sample_data():
    """创建示例数据"""
    from licenses.models import SKU, License, Setting
    from django.utils import timezone

    # 创建示例SKU
    sku_data = [
        {
            'sku_id': 'smartstrm_pro_30',
            'app': 'smartstrm',
            'renewal_days': 30,
            'name': 'SmartStrm Pro 30天',
            'price': 29.99
        },
        {
            'sku_id': 'smartstrm_pro_365',
            'app': 'smartstrm',
            'renewal_days': 365,
            'name': 'SmartStrm Pro 1年',
            'price': 299.99
        }
    ]

    for data in sku_data:
        SKU.objects.get_or_create(
            sku_id=data['sku_id'],
            defaults=data
        )

    # 创建示例许可证
    License.objects.get_or_create(
        app='smartstrm',
        email='<EMAIL>',
        defaults={
            'expires_at': timezone.now() + timezone.timedelta(days=365),
            'plan': 'pro'
        }
    )

    # 创建系统设置
    settings_data = [
        {
            'key': 'smtp_host',
            'value': 'smtp.gmail.com',
            'description': 'SMTP服务器地址'
        },
        {
            'key': 'smtp_port',
            'value': '587',
            'description': 'SMTP端口'
        },
        {
            'key': 'smtp_use_tls',
            'value': 'true',
            'description': '是否使用TLS'
        }
    ]

    for data in settings_data:
        Setting.objects.get_or_create(
            key=data['key'],
            defaults=data
        )

    print("✓ 示例数据创建成功")

if __name__ == '__main__':
    main()
