# Django许可证服务器快速启动指南

## 🚀 一键启动

```bash
cd license_server
python start.py
```

启动脚本会自动：
- 安装所需依赖
- 初始化数据库
- 创建管理员账户
- 创建示例数据
- 启动开发服务器

## 📋 手动安装步骤

如果自动安装失败，可以手动执行：

### 1. 安装依赖
```bash
pip install Django>=4.2.0
pip install pycryptodome>=3.18.0
pip install requests>=2.31.0
```

### 2. 初始化数据库
```bash
python manage.py makemigrations
python manage.py migrate
```

### 3. 创建管理员
```bash
python manage.py createsuperuser
# 用户名: admin
# 邮箱: <EMAIL>
# 密码: admin123
```

### 4. 启动服务器
```bash
python manage.py runserver 0.0.0.0:8099
```

## 🎯 访问地址

- **管理后台**: http://localhost:8099/admin/
- **API文档**: http://localhost:8099/
- **许可验证**: POST http://localhost:8099/api/license/verify
- **生成许可**: POST http://localhost:8099/api/license/generate
- **用户列表**: GET http://localhost:8099/api/users
- **Webhook**: POST http://localhost:8099/api/webhook/afdian

## 🔧 默认配置

### 管理员账户
- 用户名: `admin`
- 密码: `admin123`
- 邮箱: `<EMAIL>`

### 示例SKU
- `smartstrm_pro_30`: SmartStrm Pro 30天 - ¥29.99
- `smartstrm_pro_365`: SmartStrm Pro 1年 - ¥299.99

### 示例许可证
- 邮箱: `<EMAIL>`
- 应用: `smartstrm`
- 计划: `pro`
- 有效期: 1年

## 🧪 测试功能

```bash
# 测试服务器功能
python test_server.py

# 生成测试许可证
python manage.py generate_license <EMAIL> --days 30 --send-email
```

## 📧 邮件配置

编辑 `.env` 文件配置SMTP:

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>
```

## 🔗 爱发电集成

### 1. 配置Webhook URL
在爱发电后台设置: `http://your-domain.com/api/webhook/afdian`

### 2. 创建对应SKU
在管理后台创建与爱发电商品对应的SKU记录

### 3. 自动处理流程
- 用户支付 → 爱发电Webhook → 自动生成许可证 → 发送邮件

## 🛠️ 管理后台功能

### 许可证管理
- 查看所有许可证状态
- 批量延长许可证时间
- 手动发送许可证邮件
- 过期状态一目了然

### 订单管理
- 查看所有支付订单
- 手动处理未处理订单
- 订单状态跟踪

### SKU管理
- 商品配置管理
- 价格和续订天数设置
- 启用/禁用商品

### 系统设置
- SMTP邮件配置
- 各种系统参数

## 🔒 安全配置

### 生产环境
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=your-domain.com
```

### 密钥管理
- 更换默认的 `LICENSE_SECRET_KEY`
- 生成新的RSA密钥对
- 使用环境变量存储敏感信息

## 📊 API使用示例

### 生成许可证
```bash
curl -X POST http://localhost:8099/api/license/generate \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "days": 30,
    "plan": "pro",
    "app": "smartstrm"
  }'
```

### 模拟Webhook
```bash
curl -X POST http://localhost:8099/api/webhook/afdian \
  -H "Content-Type: application/json" \
  -d '{
    "ec": 200,
    "em": "ok",
    "data": {
      "type": "order",
      "order": {
        "out_trade_no": "test_123456",
        "user_id": "user_123",
        "plan_id": "plan_123",
        "sku_id": "smartstrm_pro_30",
        "total_amount": "29.99",
        "status": 2,
        "remark": "<EMAIL>",
        "month": 1,
        "sku_detail": [{"sku_id": "smartstrm_pro_30", "count": 1}]
      }
    }
  }'
```

## 🐛 故障排除

### 常见问题

1. **Django导入失败**
   ```bash
   pip install Django
   ```

2. **数据库错误**
   ```bash
   rm db.sqlite3
   python manage.py migrate
   ```

3. **邮件发送失败**
   - 检查SMTP配置
   - 确认邮箱应用密码

4. **Webhook接收失败**
   - 检查SKU是否存在
   - 查看服务器日志

### 查看日志
```bash
tail -f license_server.log
```

## 📞 技术支持

如有问题，请查看：
- `README.md` - 详细文档
- `license_server.log` - 服务器日志
- Django Admin - 数据管理界面
